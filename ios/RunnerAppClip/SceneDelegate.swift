//
//  SceneDelegate.swift
//  RunnerAppClip
//
//  Created by <PERSON> on 2025/6/6.
//

import UIKit
import Flutter

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?
    private var flutterEngine: FlutterEngine?
    private var flutterViewController: FlutterViewController?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }

        // Create Flutter engine for the clip
        flutterEngine = FlutterEngine(name: "clip_engine")

        // Run the Flutter engine with the clip entry point
        flutterEngine?.run(withEntrypoint: "main", libraryURI: "package:card3/main_app_clip.dart")

        // Create Flutter view controller
        flutterViewController = FlutterViewController(engine: flutterEngine!, nibName: nil, bundle: nil)

        // Set up method channel for opening main app
        setupMethodChannel()

        // Create window and set Flutter as root view controller
        window = UIWindow(windowScene: windowScene)
        window?.rootViewController = flutterViewController
        window?.makeKeyAndVisible()
    }

    private func setupMethodChannel() {
        guard let flutterViewController = flutterViewController else { return }

        let channel = FlutterMethodChannel(
            name: "card3.clip/main_app",
            binaryMessenger: flutterViewController.binaryMessenger
        )

        channel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
            switch call.method {
            case "openMainApp":
                self?.openMainApp(result: result)
            default:
                result(FlutterMethodNotImplemented)
            }
        }
    }

    private func openMainApp(result: @escaping FlutterResult) {
        // Open the main app using URL scheme
        if let url = URL(string: "card3://") {
            UIApplication.shared.open(url) { success in
                result(success)
            }
        } else {
            result(FlutterError(code: "INVALID_URL", message: "Could not create URL for main app", details: nil))
        }
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
    }

    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
    }
}

