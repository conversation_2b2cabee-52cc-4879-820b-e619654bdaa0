import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

void main() => runApp(const Card3ClipApp());

class Card3ClipApp extends StatelessWidget {
  const Card3ClipApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Card3 Clip',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        useMaterial3: true,
      ),
      home: const ClipHomePage(),
    );
  }
}

class ClipHomePage extends StatefulWidget {
  const ClipHomePage({super.key});

  @override
  State<ClipHomePage> createState() => _ClipHomePageState();
}

class _ClipHomePageState extends State<ClipHomePage> {
  static const platform = MethodChannel('card3.clip/main_app');

  Future<void> _openMainApp() async {
    try {
      await platform.invokeMethod('openMainApp');
    } on PlatformException catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to open main app: ${e.message}')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Card3 Clip'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            const Icon(
              Icons.credit_card,
              size: 80,
              color: Colors.blue,
            ),
            const SizedBox(height: 24),
            const Text(
              'Welcome to Card3 Clip!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'This is a quick preview of Card3.',
              style: TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: _openMainApp,
              icon: const Icon(Icons.launch),
              label: const Text('Open Full App'),
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
